#!/usr/bin/env python3
"""
测试脚本，用于演示picker工具的用法
"""
import pickle
import sys

def test_pickle_payload(payload_bytes):
    """测试pickle payload"""
    print(f"Payload bytes: {payload_bytes}")
    print(f"Payload length: {len(payload_bytes)}")
    
    try:
        # 反序列化payload
        result = pickle.loads(payload_bytes)
        print(f"Result: {result}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 从命令行参数读取
        payload = sys.argv[1].encode() if isinstance(sys.argv[1], str) else sys.argv[1]
    else:
        # 从stdin读取，并解析picker的输出格式
        input_data = sys.stdin.read().strip()
        # picker输出格式是 b"..." 需要解析
        if input_data.startswith('b"') and input_data.endswith('"'):
            # 去掉 b" 和 " 并解码转义字符
            payload_str = input_data[2:-1]
            payload = payload_str.encode().decode('unicode_escape').encode('latin1')
        else:
            payload = input_data.encode()

    test_pickle_payload(payload)
